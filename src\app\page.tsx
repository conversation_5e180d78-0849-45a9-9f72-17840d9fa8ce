"use client";

import React from "react";
import { motion } from "framer-motion";
import { Navigation } from "@/components/navigation";
import { HeroSection } from "@/components/hero-section";
import { GallerySection } from "@/components/gallery-section";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import Image from "next/image";
import Link from "next/link";
import { BookOpen, Users, Lightbulb, MapPin, Phone, Mail, ArrowRight } from "lucide-react";

export default function Home() {

  return (
    <div className="min-h-screen bg-white">
      <Navigation />
      <HeroSection />

      {/* Features Section */}
      <section className="py-24 bg-gray-50">
        <div className="max-w-6xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-light text-gray-900 mb-4">
              Why Choose WLC Academy
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Three pillars that define our commitment to excellence
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                icon: BookOpen,
                title: "Academic Excellence",
                description: "Innovative curriculum designed to challenge minds and inspire breakthrough thinking",
                bgColor: "bg-[#F3CC5C]",
                iconColor: "text-[#07243C]"
              },
              {
                icon: Users,
                title: "Expert Mentors",
                description: "World-class educators dedicated to nurturing potential and fostering growth",
                bgColor: "bg-[#07243C]",
                iconColor: "text-[#F3CC5C]"
              },
              {
                icon: Lightbulb,
                title: "Future Ready",
                description: "Cutting-edge facilities and technology preparing students for tomorrow's challenges",
                bgColor: "bg-[#F3CC5C]",
                iconColor: "text-[#07243C]"
              }
            ].map((feature, index) => {
              const IconComponent = feature.icon;
              return (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.2 }}
                >
                  <Card className="text-center p-8 border border-gray-100 hover:border-[#F3CC5C] transition-colors duration-200">
                    <CardContent className="p-0">
                      <div className={`w-16 h-16 ${feature.bgColor} rounded-xl flex items-center justify-center mx-auto mb-6`}>
                        <IconComponent className={`w-8 h-8 ${feature.iconColor}`} />
                      </div>
                      <h3 className="text-xl font-medium text-[#07243C] mb-3">{feature.title}</h3>
                      <p className="text-gray-600 leading-relaxed">{feature.description}</p>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      <GallerySection />

      {/* About Section */}
      <section className="py-24 bg-white">
        <div className="max-w-6xl mx-auto px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <div className="mb-8">
                <div className="inline-block px-4 py-2 bg-[#F3CC5C] rounded-full text-sm font-medium text-[#07243C] mb-6">
                  About WLC Academy
                </div>
                <h2 className="text-3xl md:text-4xl font-light text-gray-900 mb-6 leading-tight">
                  Shaping minds for a brighter tomorrow
                </h2>
              </div>

              <p className="text-lg text-gray-600 leading-relaxed mb-6">
                Founded with a vision to redefine education, WLC Academy stands at the intersection
                of innovation and excellence. We don't just teach—we inspire, challenge, and
                transform lives.
              </p>

              <p className="text-gray-600 leading-relaxed mb-8">
                Our approach goes beyond traditional learning, fostering critical thinking,
                creativity, and leadership skills that prepare students for an ever-evolving world.
              </p>

              <Button className="bg-[#07243C] text-white hover:bg-[#0a2d47]">
                <Link href="/about">Discover Our Story</Link>
              </Button>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="relative"
            >
              <Card className="p-8 border border-gray-100">
                <CardContent className="p-0">
                  <div className="grid grid-cols-1 gap-8">
                    <div className="text-center">
                      <h3 className="text-xl font-medium text-gray-900 mb-2">Our Mission</h3>
                      <p className="text-gray-600 text-sm leading-relaxed">
                        Empowering students to become confident, capable leaders who shape the future
                      </p>
                    </div>
                    <div className="text-center">
                      <h3 className="text-xl font-medium text-gray-900 mb-2">Our Vision</h3>
                      <p className="text-gray-600 text-sm leading-relaxed">
                        Setting the global standard for educational excellence and innovation
                      </p>
                    </div>
                  </div>

                  <div className="mt-8 pt-8 border-t border-gray-200">
                    <div className="grid grid-cols-2 gap-8 text-center">
                      <div>
                        <div className="text-2xl font-medium text-gray-900 mb-1">2025</div>
                        <div className="text-sm text-gray-600">Founded</div>
                      </div>
                      <div>
                        <div className="text-2xl font-medium text-gray-900 mb-1">Global</div>
                        <div className="text-sm text-gray-600">Recognition</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>



      {/* Programs Section */}
      <section className="py-24 bg-white">
        <div className="max-w-6xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <div className="inline-block px-4 py-2 bg-[#F3CC5C] rounded-full text-sm font-medium text-[#07243C] mb-6">
              Our Program
            </div>
            <h2 className="text-3xl md:text-4xl font-light text-gray-900 mb-4">
              Afterschool Excellence
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Year-round afterschool program providing academic support, enrichment activities, and a safe learning environment
            </p>
          </motion.div>

          <div className="max-w-2xl mx-auto">
            {[
              {
                icon: BookOpen,
                title: "Afterschool Program",
                description: "Comprehensive year-round afterschool program providing academic support, enrichment activities, and a safe learning environment for students ages 5-14",
                features: [
                  "Homework assistance and tutoring",
                  "STEM activities and creative arts",
                  "Physical activities and character building",
                  "Healthy snacks and safe environment"
                ],
                bgColor: "bg-[#F3CC5C]",
                iconColor: "text-[#07243C]",
                schedule: "Monday-Friday, 3:00 PM - 6:00 PM"
              }
            ].map((program, index) => {
              const IconComponent = program.icon;
              return (
                <motion.div
                  key={program.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.2 }}
                >
                  <Card className="p-8 border border-gray-100 hover:border-[#F3CC5C] transition-colors duration-200 h-full text-center">
                    <CardContent className="p-0">
                      <div className={`w-20 h-20 ${program.bgColor} rounded-xl flex items-center justify-center mb-6 mx-auto`}>
                        <IconComponent className={`w-10 h-10 ${program.iconColor}`} />
                      </div>
                      <h3 className="text-2xl font-medium text-[#07243C] mb-4">{program.title}</h3>
                      <p className="text-gray-600 leading-relaxed mb-6">{program.description}</p>

                      <div className="bg-gray-50 rounded-lg p-4 mb-6">
                        <p className="text-sm font-medium text-[#07243C] mb-2">Program Schedule</p>
                        <p className="text-sm text-gray-600">{program.schedule}</p>
                      </div>

                      <div className="space-y-2 mb-8 text-left">
                        <h4 className="font-medium text-gray-900 mb-3">Key Features:</h4>
                        {program.features.map((feature, featureIndex) => (
                          <div key={featureIndex} className="flex items-center text-sm text-gray-600">
                            <div className="w-2 h-2 bg-[#F3CC5C] rounded-full mr-3 flex-shrink-0"></div>
                            {feature}
                          </div>
                        ))}
                      </div>

                      <div className="flex flex-col sm:flex-row gap-3">
                        <Button className="flex-1 bg-[#07243C] text-white hover:bg-[#0a2d47] group">
                          <Link href="/programs">Learn More</Link>
                          <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                        </Button>
                        <Button variant="outline" className="flex-1 text-[#07243C] border-[#07243C] hover:bg-[#07243C] hover:text-white">
                          <Link href="/contact">Enroll Now</Link>
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-24 bg-gray-50">
        <div className="max-w-6xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <div className="inline-block px-4 py-2 bg-[#F3CC5C] rounded-full text-sm font-medium text-[#07243C] mb-6">
              Get in Touch
            </div>
            <h2 className="text-3xl md:text-4xl font-light text-gray-900 mb-4">
              Ready to begin your journey?
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Take the first step towards excellence. We're here to guide you every step of the way.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="space-y-8"
            >
              <div>
                <h3 className="text-xl font-medium text-gray-900 mb-6">Connect With Us</h3>
                <div className="space-y-6">
                  <div className="flex items-start">
                    <div className="w-12 h-12 bg-[#F3CC5C] rounded-xl flex items-center justify-center mr-4 flex-shrink-0">
                      <MapPin className="w-5 h-5 text-[#07243C]" />
                    </div>
                    <div>
                      <div className="text-[#07243C] font-medium mb-1">Visit Us</div>
                      <div className="text-gray-600">123 Education Street, Learning City</div>
                      <div className="text-gray-600">LC 12345, Country</div>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="w-12 h-12 bg-[#07243C] rounded-xl flex items-center justify-center mr-4 flex-shrink-0">
                      <Phone className="w-5 h-5 text-[#F3CC5C]" />
                    </div>
                    <div>
                      <div className="text-[#07243C] font-medium mb-1">Call Us</div>
                      <div className="text-gray-600">(*************</div>
                      <div className="text-gray-600">(*************</div>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="w-12 h-12 bg-[#F3CC5C] rounded-xl flex items-center justify-center mr-4 flex-shrink-0">
                      <Mail className="w-5 h-5 text-[#07243C]" />
                    </div>
                    <div>
                      <div className="text-[#07243C] font-medium mb-1">Email Us</div>
                      <div className="text-gray-600"><EMAIL></div>
                      <div className="text-gray-600"><EMAIL></div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <Card className="p-8 border border-gray-100">
                <CardContent className="p-0">
                  <h3 className="text-xl font-medium text-gray-900 mb-6">Send us a message</h3>
                  <form className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <Input
                          type="text"
                          placeholder="First Name"
                          className="w-full"
                        />
                      </div>
                      <div>
                        <Input
                          type="text"
                          placeholder="Last Name"
                          className="w-full"
                        />
                      </div>
                    </div>
                    <div>
                      <Input
                        type="email"
                        placeholder="Email Address"
                        className="w-full"
                      />
                    </div>
                    <div>
                      <Textarea
                        rows={5}
                        placeholder="Tell us about your interest in WLC Academy..."
                        className="w-full resize-none"
                      />
                    </div>
                    <Button
                      type="submit"
                      className="w-full bg-[#07243C] text-white hover:bg-[#0a2d47]"
                    >
                      Send Message
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </motion.div>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-center mt-16"
          >
            <p className="text-gray-600 mb-4">
              Want to explore more? Visit our dedicated pages for detailed information.
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <Button variant="outline" className="text-[#07243C] border-[#07243C] hover:bg-[#07243C] hover:text-white">
                <Link href="/about">Learn About Us</Link>
              </Button>
              <Button variant="outline" className="text-[#07243C] border-[#07243C] hover:bg-[#07243C] hover:text-white">
                <Link href="/programs">View Programs</Link>
              </Button>
              <Button className="bg-[#07243C] text-white hover:bg-[#0a2d47]">
                <Link href="/contact">Full Contact Page</Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-[#07243C] text-white py-16">
        <div className="max-w-6xl mx-auto px-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center mb-6">
                <Image
                  src="/images/wlc-logo.png"
                  alt="WLC Academy"
                  width={32}
                  height={32}
                  className="rounded-lg"
                />
                <span className="ml-3 text-lg font-medium">WLC Academy</span>
              </div>
              <p className="text-gray-400 leading-relaxed mb-8 max-w-md">
                Shaping minds, building futures. Where excellence meets innovation
                in education.
              </p>
              <div className="flex space-x-4">
                <a href="#" className="w-10 h-10 bg-[#F3CC5C] rounded-lg flex items-center justify-center hover:bg-[#e6b84d] transition-colors duration-200">
                  <svg className="w-4 h-4 text-[#07243C]" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                  </svg>
                </a>
                <a href="#" className="w-10 h-10 bg-[#F3CC5C] rounded-lg flex items-center justify-center hover:bg-[#e6b84d] transition-colors duration-200">
                  <svg className="w-4 h-4 text-[#07243C]" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                  </svg>
                </a>
                <a href="#" className="w-10 h-10 bg-[#F3CC5C] rounded-lg flex items-center justify-center hover:bg-[#e6b84d] transition-colors duration-200">
                  <svg className="w-4 h-4 text-[#07243C]" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z"/>
                  </svg>
                </a>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-6">Quick Links</h3>
              <ul className="space-y-3">
                <li><a href="/about" className="text-gray-400 hover:text-white transition-colors">About Us</a></li>
                <li><a href="/programs" className="text-gray-400 hover:text-white transition-colors">Afterschool Program</a></li>
                <li><a href="/contact" className="text-gray-400 hover:text-white transition-colors">Contact Us</a></li>
                <li><a href="/contact" className="text-gray-400 hover:text-white transition-colors">Contact</a></li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-6">Resources</h3>
              <ul className="space-y-3">
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Student Portal</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Parent Hub</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Academic Calendar</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">News & Events</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              &copy; 2025 WLC Academy. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-gray-400 hover:text-white transition-colors text-sm">Privacy Policy</a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors text-sm">Terms of Service</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
