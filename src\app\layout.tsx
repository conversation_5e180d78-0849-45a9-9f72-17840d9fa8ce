import type { Metadata } from "next";
import { <PERSON>, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: 'swap',
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: 'swap',
});

export const metadata: Metadata = {
  title: "WLC Academy - Excellence in Education",
  description: "Empowering students to reach their full potential through innovative learning, dedicated teaching, and a commitment to academic excellence.",
  keywords: "education, academy, learning, students, excellence, WLC Academy",
  authors: [{ name: "WLC Academy" }],
  creator: "WLC Academy",
  publisher: "WLC Academy",
  openGraph: {
    title: "WLC Academy - Excellence in Education",
    description: "Empowering students to reach their full potential through innovative learning and dedicated teaching.",
    type: "website",
    locale: "en_US",
    siteName: "WLC Academy",
  },
  twitter: {
    card: "summary_large_image",
    title: "WLC Academy - Excellence in Education",
    description: "Empowering students to reach their full potential through innovative learning and dedicated teaching.",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <link rel="icon" href="/images/wlc-logo.png" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </head>
      <body
        className={`${inter.variable} ${geistMono.variable} antialiased font-sans`}
      >
        {children}
      </body>
    </html>
  );
}
