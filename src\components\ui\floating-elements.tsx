"use client";

import React from "react";
import { motion } from "framer-motion";

export const FloatingElements = () => {
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {/* Minimalistic floating elements */}

      {/* Subtle dots */}
      <motion.div
        className="absolute top-1/4 left-[15%] w-1 h-1 bg-[#F3CC5C] rounded-full opacity-40"
        animate={{
          y: [0, -10, 0],
          opacity: [0.4, 0.8, 0.4],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />

      <motion.div
        className="absolute top-[60%] right-[20%] w-1 h-1 bg-[#07243C] rounded-full opacity-30"
        animate={{
          y: [0, -8, 0],
          opacity: [0.3, 0.6, 0.3],
        }}
        transition={{
          duration: 10,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 2,
        }}
      />

      {/* Clean lines */}
      <motion.div
        className="absolute top-[30%] right-[10%] w-8 h-[1px] bg-gradient-to-r from-transparent via-[#F3CC5C] to-transparent opacity-20"
        animate={{
          x: [0, 5, 0],
          opacity: [0.2, 0.5, 0.2],
        }}
        transition={{
          duration: 12,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1,
        }}
      />

      <motion.div
        className="absolute bottom-[40%] left-[10%] w-[1px] h-12 bg-gradient-to-t from-transparent via-[#07243C] to-transparent opacity-15"
        animate={{
          y: [0, -3, 0],
          opacity: [0.15, 0.4, 0.15],
        }}
        transition={{
          duration: 14,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 3,
        }}
      />

      {/* Subtle geometric accent */}
      <motion.div
        className="absolute top-[20%] right-[30%] w-2 h-2 border border-[#F3CC5C]/20 opacity-25"
        animate={{
          rotate: [0, 90, 0],
          scale: [1, 1.1, 1],
        }}
        transition={{
          duration: 16,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 4,
        }}
      />

      {/* Floating circle outline */}
      <motion.div
        className="absolute bottom-[30%] right-[15%] w-3 h-3 border border-[#07243C]/15 rounded-full opacity-20"
        animate={{
          y: [0, -6, 0],
          scale: [1, 1.05, 1],
        }}
        transition={{
          duration: 18,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 5,
        }}
      />
    </div>
  );
};
