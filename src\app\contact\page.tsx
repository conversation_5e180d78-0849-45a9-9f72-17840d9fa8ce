"use client";

import React from "react";
import { motion } from "framer-motion";
import { Navigation } from "@/components/navigation";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { MapPin, Phone, Mail, Clock } from "lucide-react";

export default function ContactPage() {
  return (
    <div className="min-h-screen bg-white">
      <Navigation />
      
      {/* Hero Section */}
      <section className="pt-24 pb-16 bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-6xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <div className="inline-block px-4 py-2 bg-[#F3CC5C] rounded-full text-sm font-medium text-[#07243C] mb-6">
              Get in Touch
            </div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-light text-gray-900 mb-6 leading-tight">
              Ready to begin your journey?
            </h1>
            <p className="text-lg md:text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto">
              Take the first step towards excellence. We're here to guide you every step of the way.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-24 bg-white">
        <div className="max-w-6xl mx-auto px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
            {/* Contact Information */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="space-y-8"
            >
              <div>
                <h3 className="text-2xl font-medium text-gray-900 mb-6">Connect With Us</h3>
                <div className="space-y-6">
                  <div className="flex items-start">
                    <div className="w-12 h-12 bg-[#F3CC5C] rounded-xl flex items-center justify-center mr-4 flex-shrink-0">
                      <MapPin className="w-5 h-5 text-[#07243C]" />
                    </div>
                    <div>
                      <div className="text-[#07243C] font-medium mb-1">Visit Us</div>
                      <div className="text-gray-600">123 Education Street, Learning City</div>
                      <div className="text-gray-600">LC 12345, Country</div>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="w-12 h-12 bg-[#07243C] rounded-xl flex items-center justify-center mr-4 flex-shrink-0">
                      <Phone className="w-5 h-5 text-[#F3CC5C]" />
                    </div>
                    <div>
                      <div className="text-[#07243C] font-medium mb-1">Call Us</div>
                      <div className="text-gray-600">(*************</div>
                      <div className="text-gray-600">(*************</div>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="w-12 h-12 bg-[#F3CC5C] rounded-xl flex items-center justify-center mr-4 flex-shrink-0">
                      <Mail className="w-5 h-5 text-[#07243C]" />
                    </div>
                    <div>
                      <div className="text-[#07243C] font-medium mb-1">Email Us</div>
                      <div className="text-gray-600"><EMAIL></div>
                      <div className="text-gray-600"><EMAIL></div>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="w-12 h-12 bg-[#07243C] rounded-xl flex items-center justify-center mr-4 flex-shrink-0">
                      <Clock className="w-5 h-5 text-[#F3CC5C]" />
                    </div>
                    <div>
                      <div className="text-[#07243C] font-medium mb-1">Office Hours</div>
                      <div className="text-gray-600">Monday - Friday: 8:00 AM - 5:00 PM</div>
                      <div className="text-gray-600">Saturday: 9:00 AM - 2:00 PM</div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Contact Form */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <Card className="p-8 border border-gray-100">
                <CardContent className="p-0">
                  <h3 className="text-2xl font-medium text-gray-900 mb-6">Send us a message</h3>
                  <form className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <Input
                          type="text"
                          placeholder="First Name"
                          className="w-full"
                        />
                      </div>
                      <div>
                        <Input
                          type="text"
                          placeholder="Last Name"
                          className="w-full"
                        />
                      </div>
                    </div>
                    <div>
                      <Input
                        type="email"
                        placeholder="Email Address"
                        className="w-full"
                      />
                    </div>
                    <div>
                      <Input
                        type="tel"
                        placeholder="Phone Number"
                        className="w-full"
                      />
                    </div>
                    <div>
                      <select className="w-full px-3 py-2 border border-gray-200 rounded-md focus:ring-2 focus:ring-[#F3CC5C] focus:border-transparent text-gray-900">
                        <option value="">Select Program Interest</option>
                        <option value="foundation">Foundation Years</option>
                        <option value="advanced">Advanced Studies</option>
                        <option value="leadership">Leadership Track</option>
                        <option value="general">General Inquiry</option>
                      </select>
                    </div>
                    <div>
                      <Textarea
                        rows={5}
                        placeholder="Tell us about your interest in WLC Academy..."
                        className="w-full resize-none"
                      />
                    </div>
                    <Button
                      type="submit"
                      className="w-full bg-[#07243C] text-white hover:bg-[#0a2d47]"
                    >
                      Send Message
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-24 bg-gray-50">
        <div className="max-w-6xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-light text-gray-900 mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Find answers to common questions about WLC Academy
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {[
              {
                question: "What is the admission process?",
                answer: "Our admission process includes an application, assessment, interview, and enrollment. We guide families through each step."
              },
              {
                question: "What are the class sizes?",
                answer: "We maintain small class sizes with a maximum of 15 students per class to ensure personalized attention."
              },
              {
                question: "Do you offer scholarships?",
                answer: "Yes, we offer merit-based and need-based scholarships. Contact our admissions team for more information."
              },
              {
                question: "What extracurricular activities are available?",
                answer: "We offer a wide range of activities including sports, arts, music, debate, robotics, and community service programs."
              }
            ].map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
              >
                <Card className="p-6 border border-gray-100 hover:border-[#F3CC5C] transition-colors duration-200">
                  <CardContent className="p-0">
                    <h3 className="text-lg font-medium text-gray-900 mb-3">{faq.question}</h3>
                    <p className="text-gray-600 leading-relaxed">{faq.answer}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-[#07243C] text-white py-16">
        <div className="max-w-6xl mx-auto px-6 text-center">
          <p className="text-gray-400">
            &copy; 2024 WLC Academy. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
}
